# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/
/legacySol
# production
/build
secrete.txt
# misc
.DS_Store
*.pem
./secrete.txt
/secrete.txt
/legacySol
./legacySol
# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
legacySol
# local env files
.env*.local
secrete.txt
# vercel
.vercel
project-guide.txt
# typescript
*.tsbuildinfo
next-env.d.ts
