# Project 2 – Blockchain-Based Application Design and Development

**COMP6452 Software Architecture for Blockchain Applications**  
**2025 Term 2**

## 1 Introduction

In this project, you will design and develop a blockchain-based Proof of Concept (PoC) application to solve a real-world problem in a chosen domain. You will form a team and propose a domain to focus on (an example list of domains is given in Section 2.1). It would be best to research the problems in the chosen domain, then identify requirements, design a blockchain-based architecture, and finally implement a PoC system.

This is a group project with four/five students per group. There are two presentations in Week 5 and Week 9. The first presentation should cover the chosen domain, problem, requirements, and high-level architecture design. The second presentation should cover the implementation details and a live demo of the proposed system.

## 2 Tasks

### 2.1 Task 1: Domain, Problems, and Requirements

1. Select a domain of your team's choice. Following are some potential examples:

   - (a) Food/agriculture supply chain
   - (b) Land title management
   - (c) Circular economy
   - (d) Intellectual property rights management
   - (e) Decentralised finance
   - (f) Environment, Social, and Governance (ESG)
   - (g) Any another domain

2. Discuss why the chosen domain is attractive to your team, and why the problem is worth solving.

3. Identify at least three problems/challenges of the chosen domain that could be addressed using a blockchain.

4. Identify at least three Functional Requirements (FRs) and three Non-Functional Requirements (NFRs) for the chosen domain.

5. **Task 1 submission:** Post answers to the above four questions on the WebCMS Group Wiki by Week 3 Wednesday 11:59 pm. This is a group submission and detailed instructions for how to submit Task 1 will be released in due course. You will receive feedback from one of the lecturers by the beginning of Week 4. You should reflect on the feedback and update the Wiki page by Week 4 Friday 11:59 pm.

### 2.2 Task 2: Architecture Design and Evaluation

1. Based on the feedback, reformulate any of your problems/challenges and requirements.

2. Analyse the suitability of using blockchain technology to address the identified problems/challenges of the chosen domain. You may consider questions such as (a related framework will be discussed in the class):

   - Is there a need for multi-party interactions?
   - Are operations centralised?
   - Is data immutability required?
   - Is data transparency required for effective/fair operations in the domain?
   - Is there any private data, Personally Identifiable Information (PII), or is confidentiality required commercially?
   - Is high performance and scalability required?

3. Design two high-level software architectures to fulfil the identified FRs and NFRs. Include the following:

   - Design decisions on blockchain platform selection and deployment/roles of blockchain in the system.
   - Decide on what stays on-chain and off-chain. Try to leverage off-chain computational, storage, and oracle services (e.g., price and weather oracles) whenever possible.
   - Illustrate both software architectures using suitable diagrams, e.g., component-connector and sequence diagrams are preferred.
   - Compare and contrast the two design alternatives against stated NFRs and other software quality attributes.
   - Identify the most fitting architecture design for your implementation.

4. **Task 2 submission:** Present your findings during the Week 5 presentation slot (12-min presentation + 3-min Q&A). All presentations are done in person and all team members must be present. A detailed submission guide will be released in due course. You need to indicate who did what at the beginning of the presentation. The presentation should cover the following:
   - Chosen domain, challenges and problems, and why they are worth solving (1 slide).
   - Identified 3 FRs and 3 NFRs (1 slide).
   - Analysis of the suitability of using blockchain technology to address the identified problems/challenges of the chosen domain (1 slide).
   - 2 architectural designs and comparison (max 4 slides).
   - Proposed development plan (1 slide).

### 2.3 Task 3: Development and Demo

1. Revise your chosen software architecture based on the feedback for the first presentation.

2. Develop a blockchain-based PoC software system that implements the main architecture designed in Task 2 (Section 2.2). The developed solution should:

   - (a) Interact with a blockchain.
   - (b) Include at least two smart contracts with business logic that fulfil FRs (not only as data storage).
   - (c) Include an off-chain computational component.
   - (d) Include an off-chain data storage component (e.g., cloud, peer-to-peer data storage, or shared database).
   - (e) Include an oracle where applicable. If your oracle includes (c) and/or (d) above, you can skip them.
   - (f) A GUI is unnecessary if the communication between components is clearly demonstrated.

3. Present your solution during the Week 9/10 presentation slot (2-min introduction and design + 5-min demo + 3-min code walkthrough + 5-min Q&A). All presentations are done in person and all team members must be present. You need to indicate who did what at the beginning of the presentation. The presentation should cover the following:
   - Solution design, any specific changes due to feedback from Presentation 1 (2-3 slides).
   - Implementation details (1 slide)
   - Demo.

## 3 Marking Criteria

Table 1 lists the marking criteria.

**Table 1: Marking criteria.**

| Task               | Assessment Item                                                                           | Points | Total Points |
| ------------------ | ----------------------------------------------------------------------------------------- | ------ | ------------ |
| **Presentation 1** | Chosen domain, problems, and why they are worth solving                                   | 2      | 16           |
|                    | At least 3 functional requirements                                                        | 2      |              |
|                    | At least 3 non-functional requirements                                                    | 2      |              |
|                    | Suitability of blockchain technology to address identified problems                       | 2      |              |
|                    | Main architecture design (3), alternative design (2), and comparison (2)                  | 7      |              |
|                    | Proposed development plan and reflection on the initial feedback                          | 1      |              |
| **Presentation 2** | Finalised architecture and any changes due to feedback                                    | 2      | 14           |
|                    | Implementation details – Blockchain platform, off-chain components, and tools used        | 2      |              |
|                    | Interaction with a blockchain + oracle                                                    | 2      |              |
|                    | 2 smart contracts with business logic                                                     | 2      |              |
|                    | Completeness/quality of demo                                                              | 2      |              |
|                    | Source code - Best practices (1), patterns use (1), testing (1), and code readability (1) | 4      |              |

## 4 Final Project Submission

At the end of Task 3, the slides, source code including unit tests, and addresses of smart contracts, need to be submitted to the submission page setup in WebCMS after the presentation sessions. Include a readme.txt file with specific instructions and libraries that may be needed to run your program. Upload the slides, source code, readme.txt, and smart contract addresses.txt as a single .zip or tar.gz file.

- The standard late penalty applies as per the UNSW assessment implementation procedure. The late penalty is a per-day (not hourly) mark reduction equal to 5% of the max assessment mark, for up to 5 days. Zero marks after 5 days. All days, not just Monday-Friday, are included in the days late. See the course outline for examples on how penalty is calculated.

- Plagiarism checker will be used to analyse the submitted code (changing the name of state variables will not help). UNSW has an ongoing commitment to fostering a culture of learning informed by academic integrity. All UNSW staff and students have a responsibility to adhere to this principle of academic integrity. Plagiarism undermines academic integrity and is not tolerated at UNSW.

## 5 Best Practices

Consider the following tips to improve the accuracy, quality, and accountability of the project outputs:

- Study the chosen domain a bit and try to identify significant problems that are worth solving. For example, a common mistake is to choose "supply chain" or "agriculture supply chain" as the domain without being specific about the products such as fruits, milk, and meat, which cannot be handled the same way.

- Blockchains cannot solve all problems. They are good for new trust-worthy or efficient ways of working together, especially in multi-party processes. Just because you like a domain, blockchains may not be the best tool to solve problems in that domain.

- Write requirements as complete sentences. Do not mix FRs and NFRs. When documenting them use keywords like SHOULD, MUST, MAY, etc. See key words for use in indicating requirement levels at https://www.ietf.org/rfc/rfc2119.txt

- When documenting NFRs present them using keywords in ISO/IEC 25010:2011 Quality Model (discussed in Week 2).

- Focus on significant requirements that are relevant in the context of this module, e.g., while usability is important in software, focus on properties that are more relevant to blockchains.

- Keep requirements independent of implementation details. It is a common mistake to talk about blockchain or smart contracts in the technical requirements. While a business requirement may say one must develop the system on top of a blockchain, we want to focus on objective technical requirements.

- Use the blockchain suitability test framework that will be discussed in class to determine the suitability of blockchain for the chosen domain.

- Propose a design alternative that is conceptually different from the main design.

- Component and connector diagrams are preferred over most UML diagrams, especially when it comes to comparing the main and alternative designs.

- Scope your project well. The focus should be doing a few things deep rather than lots of things shallow.

- While a UI can make your demo easier to follow, it is not the focus of this assessment, i.e., you will not get marks for having a nice UI. Focus should be on the design, smart contracts, their interactions with the real-world, testing, patterns, and best practices.

- All team members must code, e.g., should write some suitable combination of smart contract, oracle, and unit testing code.

- If you have any concerns about the project, including lack of contribution/participation from other team members, promptly raise them with the teaching team.

- Utilise version control systems (e.g., GitHub), chat platforms (Teams), and Agile project management tools (e.g., Tasks in Teams or Trello) to effectively manage your work and project communications.
