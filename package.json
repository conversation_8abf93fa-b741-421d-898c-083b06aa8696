{"name": "notion-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chainlink/contracts": "^1.1.0", "@headlessui/react": "^1.7.18", "@hookform/resolvers": "^3.9.0", "@privy-io/react-auth": "^1.65.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.1.5", "@tabler/icons-react": "^3.4.0", "@tanstack/react-query": "^5.37.1", "@web3-storage/w3up-client": "^13.1.1", "@web3modal/wagmi": "^4.2.1", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.2.4", "hardhat": "^2.22.4", "lucide-react": "^0.309.0", "next": "14.0.4", "nodemailer": "^6.9.8", "react": "^18", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-hook-form": "^7.52.1", "react-icons": "^5.0.1", "react-intersection-observer": "^9.5.3", "react-responsive": "^9.0.2", "react-select": "^5.8.0", "react-slick": "^0.29.0", "simplex-noise": "^4.0.1", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "wagmi": "^2.9.2", "zod": "^3.23.8"}, "devDependencies": {"@nomicfoundation/hardhat-chai-matchers": "^2.0.0", "@nomicfoundation/hardhat-ethers": "^3.0.0", "@nomicfoundation/hardhat-ignition": "^0.15.4", "@nomicfoundation/hardhat-ignition-ethers": "^0.15.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/node": "^20", "@types/nodemailer": "^6.4.14", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "autoprefixer": "^10.0.1", "chai": "^4.2.0", "eslint": "^8", "eslint-config-next": "14.0.4", "ethers": "5.7.2", "hardhat-gas-reporter": "^1.0.8", "postcss": "^8", "solidity-coverage": "^0.8.0", "tailwindcss": "^3.3.0", "typechain": "^8.3.0", "typescript": "^5"}}